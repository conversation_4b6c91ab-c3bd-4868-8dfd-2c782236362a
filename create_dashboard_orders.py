#!/usr/bin/env python3
"""
Simple script to create Razorpay orders that will appear on dashboard
"""

import os
import sys
import django
import time
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from packages_and_subscriptions.models import Package, Subscription
from students.models import Student
from packages_and_subscriptions.payment_service import PaymentService
from django.conf import settings
import razorpay

def create_direct_razorpay_orders():
    """Create orders directly using Razorpay client"""
    print("🚀 Creating Test Orders for Razorpay Dashboard")
    print("=" * 50)
    
    # Initialize Razorpay client
    try:
        client = razorpay.Client(auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET))
        print(f"✅ Razorpay client initialized with key: {settings.RAZORPAY_KEY_ID}")
    except Exception as e:
        print(f"❌ Failed to initialize Razorpay client: {e}")
        return
    
    # Get test data
    try:
        students = list(Student.objects.all()[:3])
        packages = list(Package.objects.filter(is_active=True, discount_price__gt=0))
        
        print(f"📊 Found {len(students)} students and {len(packages)} paid packages")
        
        if not students or not packages:
            print("❌ Need at least 1 student and 1 paid package for testing")
            return
            
    except Exception as e:
        print(f"❌ Failed to get test data: {e}")
        return
    
    orders_created = []
    
    # Create test orders
    test_scenarios = [
        {"amount": 298.00, "description": "Updated Package Name - Validity Package"},
        {"amount": 915.00, "description": "Graiden George - Premium Validity Package"},
        {"amount": 199.00, "description": "JEE Mock Test Competition - Event Package"},
        {"amount": 1499.00, "description": "6 Month Premium Access - Long Term Package"},
        {"amount": 99.00, "description": "Test Small Amount Package"},
    ]
    
    for i, scenario in enumerate(test_scenarios):
        student = students[i % len(students)]
        package = packages[i % len(packages)]
        
        print(f"\n💳 Creating Order {i+1}: {scenario['description']}")
        print(f"   Amount: ₹{scenario['amount']}")
        print(f"   Student: {student.user.username}")
        
        try:
            order_data = {
                "amount": int(scenario['amount'] * 100),  # Convert to paise
                "currency": "INR",
                "payment_capture": 1,
                "notes": {
                    "student_id": student.id,
                    "student_name": f"{student.user.first_name} {student.user.last_name}",
                    "student_email": student.user.email,
                    "package_id": package.id,
                    "package_name": package.name,
                    "package_type": package.package_type,
                    "test_transaction": "true",
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "description": scenario['description']
                }
            }
            
            order = client.order.create(order_data)
            
            print(f"   ✅ Order Created Successfully!")
            print(f"   📋 Order ID: {order['id']}")
            print(f"   💰 Amount: ₹{scenario['amount']} ({order['amount']} paise)")
            print(f"   📅 Created: {datetime.fromtimestamp(order['created_at'])}")
            
            orders_created.append({
                'order_id': order['id'],
                'amount': scenario['amount'],
                'description': scenario['description'],
                'student': student.user.username,
                'created_at': order['created_at']
            })
            
            time.sleep(1)  # Small delay between orders
            
        except Exception as e:
            print(f"   ❌ Failed to create order: {e}")
    
    # Summary
    print(f"\n📈 ORDERS CREATED SUMMARY")
    print("=" * 50)
    
    if orders_created:
        print(f"✅ Successfully created {len(orders_created)} orders")
        print(f"💰 Total amount: ₹{sum(o['amount'] for o in orders_created)}")
        
        print(f"\n🎯 ORDERS TO CHECK ON RAZORPAY DASHBOARD:")
        print("-" * 50)
        for i, order in enumerate(orders_created, 1):
            print(f"{i}. {order['order_id']}")
            print(f"   Amount: ₹{order['amount']} | {order['description']}")
            print(f"   Student: {order['student']}")
            print()
        
        print(f"🔗 RAZORPAY DASHBOARD LINKS:")
        print(f"   Main Dashboard: https://dashboard.razorpay.com/")
        print(f"   Orders Section: https://dashboard.razorpay.com/app/orders")
        print(f"   Payments Section: https://dashboard.razorpay.com/app/payments")
        
        print(f"\n💡 WHAT TO LOOK FOR:")
        print(f"   ✓ Orders with status 'created'")
        print(f"   ✓ Correct amounts (in paise: ₹1 = 100 paise)")
        print(f"   ✓ Student details in order notes")
        print(f"   ✓ Package information in notes")
        print(f"   ✓ Recent timestamps")
        
        print(f"\n🧪 TESTING PAYMENTS:")
        print(f"   • Use Razorpay test cards to complete payments")
        print(f"   • Test Card: 4111 1111 1111 1111")
        print(f"   • CVV: Any 3 digits")
        print(f"   • Expiry: Any future date")
        
    else:
        print("❌ No orders were created successfully")

if __name__ == "__main__":
    create_direct_razorpay_orders()
