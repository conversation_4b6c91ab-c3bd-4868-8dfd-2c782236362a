#!/usr/bin/env python3
"""
Integration test script for PopupBanner API
Tests the complete workflow from creation to public display
"""

import os
import sys
import django
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework import status

from contributor.models import PopupBanner, ContributorProfile
from customrcare.models import CustomrcareProfile

class PopupBannerIntegrationTest:
    def __init__(self):
        self.client = APIClient()
        self.setup_users()
        self.test_results = []
    
    def setup_users(self):
        """Create test users with different roles"""
        print("🔧 Setting up test users...")
        
        # Create contributor
        try:
            self.contributor_user = User.objects.get(username='test_contributor')
        except User.DoesNotExist:
            self.contributor_user = User.objects.create_user(
                username='test_contributor',
                email='<EMAIL>',
                password='testpass123'
            )
            ContributorProfile.objects.create(user=self.contributor_user)
        
        # Create customer care
        try:
            self.care_user = User.objects.get(username='test_care')
        except User.DoesNotExist:
            self.care_user = User.objects.create_user(
                username='test_care',
                email='<EMAIL>',
                password='testpass123'
            )
            CustomrcareProfile.objects.create(
                user=self.care_user,
                contact=1234567890
            )
        
        # Create admin
        try:
            self.admin_user = User.objects.get(username='test_admin')
        except User.DoesNotExist:
            self.admin_user = User.objects.create_superuser(
                username='test_admin',
                email='<EMAIL>',
                password='testpass123'
            )
        
        print("✅ Test users created successfully")
    
    def get_auth_token(self, username, password):
        """Get JWT token for authentication"""
        login_data = {
            'username': username,
            'password': password
        }
        
        # Try contributor login first
        response = self.client.post('/api/contributor/login/', login_data)
        if response.status_code == 200:
            return response.data['access']
        
        # Try customer care login
        response = self.client.post('/api/customrcare/login/', login_data)
        if response.status_code == 200:
            return response.data['access']
        
        return None
    
    def log_test(self, test_name, success, details=""):
        """Log test results"""
        status_icon = "✅" if success else "❌"
        print(f"{status_icon} {test_name}")
        if details:
            print(f"   {details}")
        
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details
        })
    
    def test_contributor_create_banner(self):
        """Test 1: Contributor creates a banner"""
        print("\n📝 Test 1: Contributor creates banner")
        
        # Get contributor token
        token = self.get_auth_token('test_contributor', 'testpass123')
        if not token:
            self.log_test("Get contributor token", False, "Failed to get auth token")
            return None
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # Create banner
        banner_data = {
            'title': 'Integration Test Banner',
            'description': 'Testing the complete workflow',
            'content_type': 'text_link',
            'text_content': 'This is a test banner for integration testing',
            'link_url': 'https://example.com',
            'link_text': 'Click here',
            'priority': 'medium',
            'display_duration': 5000
        }
        
        response = self.client.post('/api/contributor/popup-banners/', banner_data)
        
        if response.status_code == 201:
            banner_id = response.data['id']
            self.log_test("Contributor create banner", True, f"Banner ID: {banner_id}")
            return banner_id
        else:
            self.log_test("Contributor create banner", False, f"Status: {response.status_code}, Data: {response.data}")
            return None
    
    def test_contributor_list_own_banners(self, banner_id):
        """Test 2: Contributor can list own banners"""
        print("\n📋 Test 2: Contributor lists own banners")
        
        response = self.client.get('/api/contributor/popup-banners/')
        
        if response.status_code == 200:
            banners = response.data
            found_banner = any(banner['id'] == banner_id for banner in banners)
            self.log_test("Contributor list own banners", found_banner, f"Found {len(banners)} banners")
            return found_banner
        else:
            self.log_test("Contributor list own banners", False, f"Status: {response.status_code}")
            return False
    
    def test_public_api_no_active_banners(self):
        """Test 3: Public API shows no active banners initially"""
        print("\n🌐 Test 3: Public API (no active banners)")
        
        # Clear authentication
        self.client.credentials()
        
        response = self.client.get('/api/popup-banners/')
        
        if response.status_code == 200:
            banners = response.data
            no_banners = len(banners) == 0
            self.log_test("Public API (no active banners)", no_banners, f"Found {len(banners)} active banners")
            return no_banners
        else:
            self.log_test("Public API (no active banners)", False, f"Status: {response.status_code}")
            return False
    
    def test_customer_care_approve_banner(self, banner_id):
        """Test 4: Customer care approves banner"""
        print("\n👥 Test 4: Customer care approves banner")
        
        # Get customer care token
        token = self.get_auth_token('test_care', 'testpass123')
        if not token:
            self.log_test("Get customer care token", False, "Failed to get auth token")
            return False
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # List all banners (customer care can see all)
        response = self.client.get('/api/customrcare/popup-banners/')
        if response.status_code != 200:
            self.log_test("Customer care list banners", False, f"Status: {response.status_code}")
            return False
        
        # Approve the banner
        approval_data = {
            'approval_status': 'approved_by_care'
        }
        
        response = self.client.patch(f'/api/customrcare/popup-banners/{banner_id}/', approval_data)
        
        if response.status_code == 200:
            approval_status = response.data.get('approval_status')
            success = approval_status == 'approved_by_care'
            self.log_test("Customer care approve banner", success, f"Status: {approval_status}")
            return success
        else:
            self.log_test("Customer care approve banner", False, f"Status: {response.status_code}, Data: {response.data}")
            return False
    
    def test_customer_care_activate_banner(self, banner_id):
        """Test 5: Customer care activates banner"""
        print("\n🔄 Test 5: Customer care activates banner")
        
        activation_data = {
            'is_active': True
        }
        
        response = self.client.patch(f'/api/customrcare/popup-banners/{banner_id}/', activation_data)
        
        if response.status_code == 200:
            is_active = response.data.get('is_active')
            self.log_test("Customer care activate banner", is_active, f"Active: {is_active}")
            return is_active
        else:
            self.log_test("Customer care activate banner", False, f"Status: {response.status_code}, Data: {response.data}")
            return False
    
    def test_public_api_shows_active_banner(self, banner_id):
        """Test 6: Public API now shows the active banner"""
        print("\n🌐 Test 6: Public API shows active banner")
        
        # Clear authentication
        self.client.credentials()
        
        response = self.client.get('/api/popup-banners/')
        
        if response.status_code == 200:
            banners = response.data
            found_banner = any(banner['id'] == banner_id for banner in banners)
            self.log_test("Public API shows active banner", found_banner, f"Found {len(banners)} active banners")
            return found_banner
        else:
            self.log_test("Public API shows active banner", False, f"Status: {response.status_code}")
            return False
    
    def test_admin_override(self):
        """Test 7: Admin can override decisions"""
        print("\n👑 Test 7: Admin override capabilities")
        
        # Create another banner as contributor
        token = self.get_auth_token('test_contributor', 'testpass123')
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        banner_data = {
            'title': 'Admin Override Test Banner',
            'content_type': 'text_link',
            'text_content': 'Testing admin override',
            'link_url': 'https://example.com/admin',
            'link_text': 'Admin Test',
            'priority': 'high'
        }
        
        response = self.client.post('/api/contributor/popup-banners/', banner_data)
        if response.status_code != 201:
            self.log_test("Create banner for admin test", False, f"Status: {response.status_code}")
            return False
        
        banner_id = response.data['id']
        
        # Admin directly approves and activates
        # Note: We'll simulate admin login through Django admin or direct API
        # For now, let's test the model methods directly
        try:
            banner = PopupBanner.objects.get(id=banner_id)
            success = banner.approve_by_admin(self.admin_user)
            if success:
                banner.activate()
                banner.save()
                self.log_test("Admin override approval", True, f"Banner {banner_id} approved by admin")
                return True
            else:
                self.log_test("Admin override approval", False, "Failed to approve")
                return False
        except PopupBanner.DoesNotExist:
            self.log_test("Admin override approval", False, "Banner not found")
            return False
    
    def test_permission_boundaries(self):
        """Test 8: Permission boundaries are enforced"""
        print("\n🔒 Test 8: Permission boundaries")
        
        # Test contributor cannot access customer care endpoints
        token = self.get_auth_token('test_contributor', 'testpass123')
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/customrcare/popup-banners/')
        contributor_blocked = response.status_code == 403
        
        self.log_test("Contributor blocked from customer care API", contributor_blocked, 
                     f"Status: {response.status_code}")
        
        return contributor_blocked
    
    def run_all_tests(self):
        """Run the complete integration test suite"""
        print("🚀 Starting PopupBanner Integration Tests")
        print("=" * 50)
        
        # Test 1: Create banner
        banner_id = self.test_contributor_create_banner()
        if not banner_id:
            print("❌ Cannot continue without banner creation")
            return
        
        # Test 2: List own banners
        self.test_contributor_list_own_banners(banner_id)
        
        # Test 3: Public API (no active banners)
        self.test_public_api_no_active_banners()
        
        # Test 4: Customer care approval
        approved = self.test_customer_care_approve_banner(banner_id)
        
        # Test 5: Customer care activation
        if approved:
            self.test_customer_care_activate_banner(banner_id)
        
        # Test 6: Public API shows active banner
        self.test_public_api_shows_active_banner(banner_id)
        
        # Test 7: Admin override
        self.test_admin_override()
        
        # Test 8: Permission boundaries
        self.test_permission_boundaries()
        
        # Summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['details']}")
        
        print("\n🎉 Integration testing completed!")

if __name__ == "__main__":
    test_runner = PopupBannerIntegrationTest()
    test_runner.run_all_tests()
