#!/usr/bin/env python3
"""
Customer Care Registration Test Script

This script tests that customer care users can register without authentication
and that proper validation is enforced.
"""

import os
import sys
import django
from django.conf import settings

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework.test import APIClient
from customrcare.models import CustomrcareProfile
import json
import uuid

class CustomerCareRegistrationTester:
    def __init__(self):
        self.client = APIClient()
        self.test_results = []
        
    def log_result(self, test_name, expected, actual, passed):
        """Log test results"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'expected': expected,
            'actual': actual,
            'status': status
        })
        print(f"{status} {test_name}")
        if not passed:
            print(f"   Expected: {expected}")
            print(f"   Actual: {actual}")
    
    def cleanup_test_users(self):
        """Clean up any existing test users"""
        test_usernames = [
            'test_care_reg_user', 'test_care_reg_user2', 'test_care_reg_user3'
        ]
        User.objects.filter(username__in=test_usernames).delete()
        print("🧹 Cleaned up existing test users")
    
    def test_successful_registration(self):
        """Test successful customer care registration"""
        print("\n✅ Testing successful registration...")
        
        registration_data = {
            "user": {
                "username": "test_care_reg_user",
                "password": "SecurePass123",
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "Care"
            },
            "contact": 9876543210,
            "role": "customrcare"
        }
        
        response = self.client.post('/api/customrcare/register/', data=registration_data, format='json')
        
        self.log_result(
            "Customer care registration succeeds",
            201,
            response.status_code,
            response.status_code == 201
        )
        
        if response.status_code == 201:
            response_data = response.json()
            
            # Check if JWT tokens are provided
            has_refresh = 'refresh' in response_data
            has_access = 'access' in response_data
            
            self.log_result(
                "Registration returns refresh token",
                True,
                has_refresh,
                has_refresh
            )
            
            self.log_result(
                "Registration returns access token",
                True,
                has_access,
                has_access
            )
            
            # Check if user was created in database
            user_exists = User.objects.filter(username="test_care_reg_user").exists()
            self.log_result(
                "User created in database",
                True,
                user_exists,
                user_exists
            )
            
            # Check if profile was created
            if user_exists:
                user = User.objects.get(username="test_care_reg_user")
                profile_exists = CustomrcareProfile.objects.filter(user=user).exists()
                self.log_result(
                    "Customer care profile created",
                    True,
                    profile_exists,
                    profile_exists
                )
                
                if profile_exists:
                    profile = CustomrcareProfile.objects.get(user=user)
                    correct_role = profile.role == "customrcare"
                    self.log_result(
                        "Profile has correct role",
                        True,
                        correct_role,
                        correct_role
                    )
    
    def test_validation_errors(self):
        """Test validation errors for invalid data"""
        print("\n🚫 Testing validation errors...")
        
        # Test with weak password
        weak_password_data = {
            "user": {
                "username": "test_care_reg_user2",
                "password": "123",  # Too weak
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "Care"
            },
            "contact": 9876543211,
            "role": "customrcare"
        }
        
        response = self.client.post('/api/customrcare/register/', data=weak_password_data, format='json')
        
        self.log_result(
            "Weak password rejected",
            400,
            response.status_code,
            response.status_code == 400
        )
        
        # Test with short username
        short_username_data = {
            "user": {
                "username": "test",  # Too short
                "password": "SecurePass123",
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "Care"
            },
            "contact": 9876543212,
            "role": "customrcare"
        }
        
        response = self.client.post('/api/customrcare/register/', data=short_username_data, format='json')
        
        self.log_result(
            "Short username rejected",
            400,
            response.status_code,
            response.status_code == 400
        )
    
    def test_duplicate_registration(self):
        """Test that duplicate usernames are rejected"""
        print("\n🔄 Testing duplicate registration prevention...")
        
        # First registration
        registration_data = {
            "user": {
                "username": "test_care_reg_user3",
                "password": "SecurePass123",
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "Care"
            },
            "contact": 9876543213,
            "role": "customrcare"
        }
        
        response1 = self.client.post('/api/customrcare/register/', data=registration_data, format='json')
        
        self.log_result(
            "First registration succeeds",
            201,
            response1.status_code,
            response1.status_code == 201
        )
        
        # Duplicate registration with same username
        duplicate_data = {
            "user": {
                "username": "test_care_reg_user3",  # Same username
                "password": "AnotherPass123",
                "email": "<EMAIL>",
                "first_name": "Another",
                "last_name": "User"
            },
            "contact": 9876543214,
            "role": "customrcare"
        }
        
        response2 = self.client.post('/api/customrcare/register/', data=duplicate_data, format='json')
        
        self.log_result(
            "Duplicate username rejected",
            400,
            response2.status_code,
            response2.status_code == 400
        )
    
    def test_login_after_registration(self):
        """Test that users can login after registration"""
        print("\n🔐 Testing login after registration...")
        
        # Try to login with the registered user
        login_data = {
            "username": "test_care_reg_user",
            "password": "SecurePass123"
        }
        
        response = self.client.post('/api/customrcare/login/', data=login_data)
        
        self.log_result(
            "Login after registration succeeds",
            200,
            response.status_code,
            response.status_code == 200
        )
        
        if response.status_code == 200:
            response_data = response.json()
            correct_role = response_data.get('role') == 'customrcare'
            self.log_result(
                "Login returns correct role",
                True,
                correct_role,
                correct_role
            )
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*60)
        print("🔧 CUSTOMER CARE REGISTRATION TEST SUMMARY")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if "PASS" in result['status'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if "FAIL" in result['status']:
                    print(f"   - {result['test']}")
        else:
            print("\n🎉 ALL TESTS PASSED! Customer care registration is working correctly.")
    
    def run_all_tests(self):
        """Run all registration tests"""
        print("🔧 Starting Customer Care Registration Tests")
        print("="*60)
        
        self.cleanup_test_users()
        self.test_successful_registration()
        self.test_validation_errors()
        self.test_duplicate_registration()
        self.test_login_after_registration()
        self.print_summary()
        
        # Cleanup after tests
        self.cleanup_test_users()

if __name__ == "__main__":
    tester = CustomerCareRegistrationTester()
    tester.run_all_tests()
