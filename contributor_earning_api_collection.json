{"info": {"name": "Contributor Earning System API", "description": "Complete API collection for testing contributor earning system", "version": "1.0.0"}, "item": [{"name": "Dashboard API", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzMTkzMDQ1LCJpYXQiOjE3NTMxNzUwNDUsImp0aSI6IjZiMDQ3YzExZDk3OTRmNTdiNjI5MTI4MDc4MGQxZjQwIiwidXNlcl9pZCI6MjE2fQ.PceC-OSI9vuWUujZyqTtG-9EEmv4pVpnrRK4oPbdx2s"}], "url": {"raw": "{{base_url}}/api/contributor/dashboard/", "host": ["{{base_url}}"], "path": ["api", "contributor", "dashboard"]}, "description": "Get contributor dashboard with earnings information"}, "response": [{"name": "Success Response (200)", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzMTkzMDQ1LCJpYXQiOjE3NTMxNzUwNDUsImp0aSI6IjZiMDQ3YzExZDk3OTRmNTdiNjI5MTI4MDc4MGQxZjQwIiwidXNlcl9pZCI6MjE2fQ.PceC-OSI9vuWUujZyqTtG-9EEmv4pVpnrRK4oPbdx2s"}], "url": {"raw": "{{base_url}}/api/contributor/dashboard/", "host": ["{{base_url}}"], "path": ["api", "contributor", "dashboard"]}}, "status": "200", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"contributor\": \"test_contributor_1\",\n  \"questions_summary\": {\n    \"questions\": {\n      \"total\": {\n        \"created\": 8,\n        \"approved\": 8,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_questions\": {\n      \"total\": {\n        \"created\": 7,\n        \"approved\": 7,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_options\": {\n      \"total\": {\n        \"created\": 8,\n        \"approved\": 8,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"blogs\": {\n      \"total\": {\n        \"created\": 1,\n        \"approved\": 1,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"previous_questions\": {\n      \"total\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    }\n  },\n  \"current_month_data\": {\n    \"questions\": {\n      \"daily\": {\n        \"created\": 8,\n        \"approved\": 8,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"weekly\": {\n        \"created\": 8,\n        \"approved\": 8,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"monthly\": {\n        \"created\": 8,\n        \"approved\": 8,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_questions\": {\n      \"daily\": {\n        \"created\": 7,\n        \"approved\": 7,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"weekly\": {\n        \"created\": 7,\n        \"approved\": 7,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"monthly\": {\n        \"created\": 7,\n        \"approved\": 7,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"blogs\": {\n      \"daily\": {\n        \"created\": 1,\n        \"approved\": 1,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"weekly\": {\n        \"created\": 1,\n        \"approved\": 1,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"monthly\": {\n        \"created\": 1,\n        \"approved\": 1,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_options\": {\n      \"daily\": {\n        \"created\": 8,\n        \"approved\": 8,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"weekly\": {\n        \"created\": 8,\n        \"approved\": 8,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"monthly\": {\n        \"created\": 8,\n        \"approved\": 8,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"previous_questions\": {\n      \"daily\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"weekly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    }\n  },\n  \"previous_month_data\": {\n    \"questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"blogs\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_options\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"previous_questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    }\n  },\n  \"third_month_data\": {\n    \"questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"blogs\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_options\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"previous_questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    }\n  },\n  \"current_month_points\": {\n    \"normal_questions\": 8,\n    \"master_questions\": 14,\n    \"master_options\": 16,\n    \"blogs\": 5,\n    \"previous_questions\": 0,\n    \"total_points\": 43\n  },\n  \"previous_month_points\": {\n    \"normal_questions\": 0,\n    \"master_questions\": 0,\n    \"master_options\": 0,\n    \"blogs\": 0,\n    \"previous_questions\": 0,\n    \"total_points\": 0\n  },\n  \"third_month_points\": {\n    \"normal_questions\": 0,\n    \"master_questions\": 0,\n    \"master_options\": 0,\n    \"blogs\": 0,\n    \"previous_questions\": 0,\n    \"total_points\": 0\n  },\n  \"earnings\": {\n    \"current_month_earnings\": {\n      \"period_type\": \"monthly\",\n      \"period_start\": \"2025-07-01T00:00:00Z\",\n      \"period_end\": \"2025-07-31T23:59:59.999999Z\",\n      \"total_points\": 419.0,\n      \"total_earnings\": 0.0,\n      \"activity_breakdown\": {\n        \"normal_questions\": {\n          \"count\": 8,\n          \"points\": 120.0\n        },\n        \"master_questions\": {\n          \"count\": 7,\n          \"points\": 210.0\n        },\n        \"master_options\": {\n          \"count\": 8,\n          \"points\": 64.0\n        },\n        \"blogs\": {\n          \"count\": 1,\n          \"points\": 25.0\n        },\n        \"previous_questions\": {\n          \"count\": 0,\n          \"points\": 0.0\n        }\n      },\n      \"is_paid\": false,\n      \"paid_at\": null\n    },\n    \"total_lifetime_earnings\": {\n      \"total_points\": 838.0,\n      \"total_earnings\": 0.0,\n      \"earning_records_count\": 2\n    },\n    \"points_configuration\": {\n      \"name\": \"VIP Points\",\n      \"is_custom\": true,\n      \"normal_questions\": 15,\n      \"master_questions\": 30,\n      \"master_options\": 8,\n      \"blogs\": 25,\n      \"previous_questions\": 12\n    },\n    \"earning_status\": {\n      \"is_paid\": false,\n      \"paid_at\": null\n    }\n  }\n}"}]}, {"name": "Earnings List API", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzMTkzMDQ5LCJpYXQiOjE3NTMxNzUwNDksImp0aSI6IjhiZTc2MDRmMmRhYTQwMWZhNjcyMjc4OWVlMDYzYmZlIiwidXNlcl9pZCI6MjE2fQ.oSneYqYeaQI7At_NSVqq5vncSnr46_lqBp8WOk_eF_Y"}], "url": {"raw": "{{base_url}}/api/contributor/earnings/", "host": ["{{base_url}}"], "path": ["api", "contributor", "earnings"]}, "description": "Get list of contributor earnings"}, "response": [{"name": "Success Response (200)", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzMTkzMDQ5LCJpYXQiOjE3NTMxNzUwNDksImp0aSI6IjhiZTc2MDRmMmRhYTQwMWZhNjcyMjc4OWVlMDYzYmZlIiwidXNlcl9pZCI6MjE2fQ.oSneYqYeaQI7At_NSVqq5vncSnr46_lqBp8WOk_eF_Y"}], "url": {"raw": "{{base_url}}/api/contributor/earnings/", "host": ["{{base_url}}"], "path": ["api", "contributor", "earnings"]}}, "status": "200", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": 5,\n    \"contributor\": 71,\n    \"contributor_username\": \"test_contributor_1\",\n    \"period_type\": \"weekly\",\n    \"period_start\": \"2025-07-21T05:30:00+05:30\",\n    \"period_end\": \"2025-07-28T05:29:59.999999+05:30\",\n    \"normal_questions_count\": 8,\n    \"master_questions_count\": 7,\n    \"master_options_count\": 8,\n    \"blogs_count\": 1,\n    \"previous_questions_count\": 0,\n    \"normal_questions_points\": \"120.00\",\n    \"master_questions_points\": \"210.00\",\n    \"master_options_points\": \"64.00\",\n    \"blogs_points\": \"25.00\",\n    \"previous_questions_points\": \"0.00\",\n    \"total_points\": \"419.00\",\n    \"total_earnings\": \"0.00\",\n    \"points_config_used\": 8,\n    \"points_config_name\": \"VIP Points\",\n    \"is_paid\": false,\n    \"paid_at\": null,\n    \"created_at\": \"2025-07-22T14:34:05.141757+05:30\",\n    \"updated_at\": \"2025-07-22T14:34:05.144339+05:30\",\n    \"activity_breakdown\": {\n      \"normal_questions\": {\n        \"count\": 8,\n        \"points\": 120.0\n      },\n      \"master_questions\": {\n        \"count\": 7,\n        \"points\": 210.0\n      },\n      \"master_options\": {\n        \"count\": 8,\n        \"points\": 64.0\n      },\n      \"blogs\": {\n        \"count\": 1,\n        \"points\": 25.0\n      },\n      \"previous_questions\": {\n        \"count\": 0,\n        \"points\": 0.0\n      }\n    }\n  },\n  {\n    \"id\": 4,\n    \"contributor\": 71,\n    \"contributor_username\": \"test_contributor_1\",\n    \"period_type\": \"monthly\",\n    \"period_start\": \"2025-07-01T05:30:00+05:30\",\n    \"period_end\": \"2025-08-01T05:29:59.999999+05:30\",\n    \"normal_questions_count\": 8,\n    \"master_questions_count\": 7,\n    \"master_options_count\": 8,\n    \"blogs_count\": 1,\n    \"previous_questions_count\": 0,\n    \"normal_questions_points\": \"120.00\",\n    \"master_questions_points\": \"210.00\",\n    \"master_options_points\": \"64.00\",\n    \"blogs_points\": \"25.00\",\n    \"previous_questions_points\": \"0.00\",\n    \"total_points\": \"419.00\",\n    \"total_earnings\": \"0.00\",\n    \"points_config_used\": 8,\n    \"points_config_name\": \"VIP Points\",\n    \"is_paid\": false,\n    \"paid_at\": null,\n    \"created_at\": \"2025-07-22T14:34:05.136861+05:30\",\n    \"updated_at\": \"2025-07-22T14:34:09.141432+05:30\",\n    \"activity_breakdown\": {\n      \"normal_questions\": {\n        \"count\": 8,\n        \"points\": 120.0\n      },\n      \"master_questions\": {\n        \"count\": 7,\n        \"points\": 210.0\n      },\n      \"master_options\": {\n        \"count\": 8,\n        \"points\": 64.0\n      },\n      \"blogs\": {\n        \"count\": 1,\n        \"points\": 25.0\n      },\n      \"previous_questions\": {\n        \"count\": 0,\n        \"points\": 0.0\n      }\n    }\n  }\n]"}]}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}]}