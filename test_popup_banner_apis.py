#!/usr/bin/env python3
"""
Comprehensive API test script for PopupBanner with text_only support
Tests create, read, update, delete operations and all content types
"""

import os
import sys
import django
import json
import requests
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.contrib.auth.models import User
from contributor.models import PopupBanner, ContributorProfile
from customrcare.models import CustomrcareProfile

class PopupBannerAPITester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.setup_test_users()
        self.test_results = []
    
    def setup_test_users(self):
        """Create test users if they don't exist"""
        print("🔧 Setting up test users...")
        
        # Create contributor
        try:
            self.contributor_user = User.objects.get(username='api_test_contributor')
        except User.DoesNotExist:
            self.contributor_user = User.objects.create_user(
                username='api_test_contributor',
                email='<EMAIL>',
                password='testpass123'
            )
            ContributorProfile.objects.create(user=self.contributor_user)
        
        # Create customer care
        try:
            self.care_user = User.objects.get(username='api_test_care')
        except User.DoesNotExist:
            self.care_user = User.objects.create_user(
                username='api_test_care',
                email='<EMAIL>',
                password='testpass123'
            )
            CustomrcareProfile.objects.create(
                user=self.care_user,
                contact=9876543210
            )
        
        print("✅ Test users ready")
    
    def get_auth_token(self, username, password, user_type="contributor"):
        """Get JWT token for authentication"""
        login_url = f"{self.base_url}/api/{user_type}/login/"
        login_data = {
            'username': username,
            'password': password
        }
        
        try:
            response = requests.post(login_url, json=login_data)
            if response.status_code == 200:
                return response.json()['access']
            else:
                print(f"❌ Login failed: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"❌ Login error: {e}")
            return None
    
    def log_test(self, test_name, success, details="", response_data=None):
        """Log test results"""
        status_icon = "✅" if success else "❌"
        print(f"{status_icon} {test_name}")
        if details:
            print(f"   {details}")
        if response_data and not success:
            print(f"   Response: {response_data}")
        
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details
        })
    
    def test_create_text_only_banner(self):
        """Test 1: Create text-only banner"""
        print("\n📝 Test 1: Create text-only banner")
        
        token = self.get_auth_token('api_test_contributor', 'testpass123', 'contributor')
        if not token:
            self.log_test("Get contributor token", False, "Failed to authenticate")
            return None
        
        headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
        
        banner_data = {
            "title": "Text Only Banner",
            "description": "Simple text banner for testing",
            "content_type": "text_only",
            "text_content": "Welcome to our platform! This is a simple text banner.",
            "priority": "medium",
            "display_duration": 5000
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/contributor/popup-banners/",
                json=banner_data,
                headers=headers
            )
            
            if response.status_code == 201:
                banner_id = response.json()['id']
                self.log_test("Create text-only banner", True, f"Banner ID: {banner_id}")
                return banner_id
            else:
                self.log_test("Create text-only banner", False, 
                            f"Status: {response.status_code}", response.json())
                return None
        except Exception as e:
            self.log_test("Create text-only banner", False, f"Error: {e}")
            return None
    
    def test_create_all_content_types(self):
        """Test 2: Create banners with all content types"""
        print("\n📝 Test 2: Create banners with all content types")
        
        token = self.get_auth_token('api_test_contributor', 'testpass123', 'contributor')
        headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
        
        test_banners = [
            {
                "title": "Image Only Banner",
                "content_type": "image_only",
                "description": "Banner with only image"
                # Note: Would need actual image file for real test
            },
            {
                "title": "Text + Link Banner",
                "content_type": "text_link",
                "text_content": "Check out our special offers!",
                "link_url": "https://example.com/offers",
                "link_text": "View Offers",
                "priority": "high"
            },
            {
                "title": "Link + Anchor Banner",
                "content_type": "link_anchor",
                "link_url": "https://example.com/external",
                "link_text": "External Link",
                "anchor_tag": "target='_blank' rel='noopener'",
                "priority": "low"
            }
        ]
        
        created_banners = []
        for banner_data in test_banners:
            try:
                response = requests.post(
                    f"{self.base_url}/api/contributor/popup-banners/",
                    json=banner_data,
                    headers=headers
                )
                
                if response.status_code == 201:
                    banner_id = response.json()['id']
                    created_banners.append(banner_id)
                    self.log_test(f"Create {banner_data['content_type']} banner", True, 
                                f"Banner ID: {banner_id}")
                else:
                    self.log_test(f"Create {banner_data['content_type']} banner", False,
                                f"Status: {response.status_code}", response.json())
            except Exception as e:
                self.log_test(f"Create {banner_data['content_type']} banner", False, f"Error: {e}")
        
        return created_banners
    
    def test_list_own_banners(self):
        """Test 3: List contributor's own banners"""
        print("\n📋 Test 3: List contributor's own banners")
        
        token = self.get_auth_token('api_test_contributor', 'testpass123', 'contributor')
        headers = {'Authorization': f'Bearer {token}'}
        
        try:
            response = requests.get(
                f"{self.base_url}/api/contributor/popup-banners/",
                headers=headers
            )
            
            if response.status_code == 200:
                banners = response.json()
                self.log_test("List own banners", True, f"Found {len(banners)} banners")
                return banners
            else:
                self.log_test("List own banners", False, f"Status: {response.status_code}")
                return []
        except Exception as e:
            self.log_test("List own banners", False, f"Error: {e}")
            return []
    
    def test_update_banner(self, banner_id):
        """Test 4: Update banner"""
        print(f"\n✏️ Test 4: Update banner {banner_id}")
        
        token = self.get_auth_token('api_test_contributor', 'testpass123', 'contributor')
        headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
        
        update_data = {
            "title": "Updated Text Only Banner",
            "description": "Updated description for testing",
            "text_content": "This banner has been updated with new content!",
            "priority": "high",
            "display_duration": 7000
        }
        
        try:
            response = requests.put(
                f"{self.base_url}/api/contributor/popup-banners/{banner_id}/",
                json=update_data,
                headers=headers
            )
            
            if response.status_code == 200:
                updated_banner = response.json()
                self.log_test("Update banner", True, 
                            f"Title: {updated_banner.get('title')}, Priority: {updated_banner.get('priority')}")
                return updated_banner
            else:
                self.log_test("Update banner", False, f"Status: {response.status_code}", response.json())
                return None
        except Exception as e:
            self.log_test("Update banner", False, f"Error: {e}")
            return None
    
    def test_customer_care_operations(self, banner_id):
        """Test 5: Customer care operations"""
        print(f"\n👥 Test 5: Customer care operations on banner {banner_id}")
        
        token = self.get_auth_token('api_test_care', 'testpass123', 'customrcare')
        if not token:
            self.log_test("Get customer care token", False, "Failed to authenticate")
            return False
        
        headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
        
        # List all banners
        try:
            response = requests.get(
                f"{self.base_url}/api/customrcare/popup-banners/",
                headers=headers
            )
            
            if response.status_code == 200:
                banners = response.json()
                self.log_test("Customer care list all banners", True, f"Found {len(banners)} banners")
            else:
                self.log_test("Customer care list all banners", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Customer care list all banners", False, f"Error: {e}")
            return False
        
        # Approve banner
        try:
            approval_data = {"approval_status": "approved_by_care"}
            response = requests.patch(
                f"{self.base_url}/api/customrcare/popup-banners/{banner_id}/",
                json=approval_data,
                headers=headers
            )
            
            if response.status_code == 200:
                self.log_test("Customer care approve banner", True, "Banner approved")
            else:
                self.log_test("Customer care approve banner", False, 
                            f"Status: {response.status_code}", response.json())
                return False
        except Exception as e:
            self.log_test("Customer care approve banner", False, f"Error: {e}")
            return False
        
        # Activate banner
        try:
            activation_data = {"is_active": True}
            response = requests.patch(
                f"{self.base_url}/api/customrcare/popup-banners/{banner_id}/",
                json=activation_data,
                headers=headers
            )
            
            if response.status_code == 200:
                self.log_test("Customer care activate banner", True, "Banner activated")
                return True
            else:
                self.log_test("Customer care activate banner", False, 
                            f"Status: {response.status_code}", response.json())
                return False
        except Exception as e:
            self.log_test("Customer care activate banner", False, f"Error: {e}")
            return False
    
    def test_public_api(self):
        """Test 6: Public API"""
        print("\n🌐 Test 6: Public API")
        
        try:
            response = requests.get(f"{self.base_url}/api/popup-banners/")
            
            if response.status_code == 200:
                banners = response.json()
                self.log_test("Public API access", True, f"Found {len(banners)} active banners")
                
                # Check if banners have correct fields for public display
                if banners:
                    first_banner = banners[0]
                    required_fields = ['id', 'title', 'content_type', 'text_content']
                    has_required_fields = all(field in first_banner for field in required_fields)
                    self.log_test("Public API data structure", has_required_fields, 
                                "Contains required fields for display")
                
                return banners
            else:
                self.log_test("Public API access", False, f"Status: {response.status_code}")
                return []
        except Exception as e:
            self.log_test("Public API access", False, f"Error: {e}")
            return []
    
    def test_statistics_api(self):
        """Test 7: Statistics API"""
        print("\n📊 Test 7: Statistics API")
        
        token = self.get_auth_token('api_test_contributor', 'testpass123', 'contributor')
        headers = {'Authorization': f'Bearer {token}'}
        
        try:
            response = requests.get(
                f"{self.base_url}/api/contributor/popup-banners/stats/",
                headers=headers
            )
            
            if response.status_code == 200:
                stats = response.json()
                self.log_test("Statistics API", True, 
                            f"Total banners: {stats.get('total_banners', 0)}")
                return stats
            else:
                self.log_test("Statistics API", False, f"Status: {response.status_code}")
                return None
        except Exception as e:
            self.log_test("Statistics API", False, f"Error: {e}")
            return None
    
    def run_all_tests(self):
        """Run comprehensive API tests"""
        print("🚀 Starting Comprehensive PopupBanner API Tests")
        print("=" * 60)
        
        # Test 1: Create text-only banner
        banner_id = self.test_create_text_only_banner()
        
        # Test 2: Create all content types
        self.test_create_all_content_types()
        
        # Test 3: List own banners
        self.test_list_own_banners()
        
        # Test 4: Update banner
        if banner_id:
            self.test_update_banner(banner_id)
        
        # Test 5: Customer care operations
        if banner_id:
            self.test_customer_care_operations(banner_id)
        
        # Test 6: Public API
        self.test_public_api()
        
        # Test 7: Statistics
        self.test_statistics_api()
        
        # Summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 API TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['details']}")
        
        print("\n🎉 API testing completed!")
        
        # Show example usage
        print("\n📋 Example API Usage:")
        print("Create text-only banner:")
        print("""
curl -X POST "http://localhost:8000/api/contributor/popup-banners/" \\
  -H "Authorization: Bearer YOUR_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "title": "Welcome Banner",
    "description": "Simple welcome message",
    "content_type": "text_only",
    "text_content": "Welcome to our platform!",
    "priority": "high"
  }'
        """)

if __name__ == "__main__":
    tester = PopupBannerAPITester()
    tester.run_all_tests()
