#!/usr/bin/env python3
"""
Comprehensive Test Script for the Contributor Earning System
Creates 50+ random entries and tests all APIs with detailed documentation generation.
"""

import os
import sys
import django
import random
import json
from datetime import datetime, timedelta
from decimal import Decimal

# Setup Django environment
sys.path.append('/Users/<USER>/Documents/code/shash_b')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Import after Django setup
from django.contrib.auth.models import User
from django.utils import timezone
from django.test import Client
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from contributor.models import ContributorProfile, ContributorPoints, ContributorEarning
from contributor.earning_service import EarningCalculationService
from questions.models import Question, MasterQuestion, MasterOption, PreviousYearQuestion, Subject, Topic, SubTopic, Course, SubCourse
from blogs.models import BlogPost


class ComprehensiveEarningTester:
    """Comprehensive test class for the earning system with API testing"""
    
    def __init__(self):
        self.test_results = []
        self.contributors = []
        self.points_configs = []
        self.api_client = APIClient()
        self.postman_data = {
            "info": {
                "name": "Contributor Earning System API",
                "description": "Complete API collection for testing contributor earning system",
                "version": "1.0.0"
            },
            "item": []
        }
    
    def log_result(self, test_name, success, message="", details=None):
        """Log test result with optional details"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'details': details or {}
        }
        self.test_results.append(result)
        print(f"{status}: {test_name} - {message}")
        if details:
            print(f"   Details: {details}")
    
    def create_test_data(self):
        """Create comprehensive test data with 50+ entries"""
        print("🔧 Creating comprehensive test data...")
        
        # Create subjects and topics for questions
        subjects = []
        for i in range(5):
            subject = Subject.objects.create(
                name=f"Test Subject {i+1}",
                description=f"Description for test subject {i+1}"
            )
            subjects.append(subject)
            
            # Create topics for each subject
            for j in range(3):
                Topic.objects.create(
                    subject=subject,
                    name=f"Topic {j+1} for {subject.name}",
                    description=f"Description for topic {j+1}"
                )
        
        # Create courses and subcourses
        courses = []
        for i in range(3):
            course = Course.objects.create(
                name=f"Test Course {i+1}",
                description=f"Description for test course {i+1}"
            )
            courses.append(course)
            
            SubCourse.objects.create(
                course=course,
                name=f"Test SubCourse {i+1}",
                description=f"Description for test subcourse {i+1}"
            )
        
        # Create multiple point configurations
        point_configs = [
            {"name": "Basic Points", "normal": 5, "master": 10, "options": 3, "blogs": 8, "previous": 4},
            {"name": "Premium Points", "normal": 10, "master": 20, "options": 5, "blogs": 15, "previous": 8},
            {"name": "VIP Points", "normal": 15, "master": 30, "options": 8, "blogs": 25, "previous": 12},
        ]
        
        for config in point_configs:
            points_config = ContributorPoints.objects.create(
                name=config["name"],
                normal_questions=config["normal"],
                master_questions=config["master"],
                master_options=config["options"],
                blogs=config["blogs"],
                previous_questions=config["previous"]
            )
            self.points_configs.append(points_config)
        
        # Create 10 test contributors
        for i in range(10):
            user = User.objects.create_user(
                username=f'test_contributor_{i+1}',
                email=f'contributor{i+1}@test.com',
                password='testpass123',
                first_name=f'Test{i+1}',
                last_name='Contributor'
            )
            
            contributor = ContributorProfile.objects.create(
                user=user,
                role='contributor'
            )
            
            # Assign random custom points to some contributors
            if i % 3 == 0:  # Every 3rd contributor gets custom points
                contributor.custom_points = random.choice(self.points_configs)
                contributor.save()
            
            self.contributors.append(contributor)
        
        self.log_result("Test Data Creation", True, 
                       f"Created {len(self.contributors)} contributors, {len(subjects)} subjects, "
                       f"{len(courses)} courses, {len(self.points_configs)} point configs")
        
        return True
    
    def create_random_activities(self):
        """Create 50+ random activities for contributors"""
        print("📝 Creating random activities...")
        
        activities_created = {
            'questions': 0,
            'master_questions': 0,
            'master_options': 0,
            'blogs': 0,
            'previous_questions': 0
        }
        
        subjects = list(Subject.objects.all())
        topics = list(Topic.objects.all())
        courses = list(Course.objects.all())
        
        # Create activities over the past 3 months
        end_date = timezone.now()
        start_date = end_date - timedelta(days=90)
        
        for contributor in self.contributors:
            # Random number of activities per contributor (5-15 each type)
            
            # Create normal questions
            for _ in range(random.randint(5, 15)):
                created_at = start_date + timedelta(
                    seconds=random.randint(0, int((end_date - start_date).total_seconds()))
                )
                question = Question.objects.create(
                    content=f"Test question by {contributor.user.username} - {random.randint(1000, 9999)}",
                    difficulty=random.randint(1, 5),
                    author=contributor,
                    status='active',
                    approval_status='approved',
                    created_at=created_at
                )
                question.subject.set(random.sample(subjects, random.randint(1, 2)))
                question.topic.set(random.sample(topics, random.randint(1, 3)))
                question.course.set(random.sample(courses, random.randint(1, 2)))
                activities_created['questions'] += 1
            
            # Create master questions
            for _ in range(random.randint(2, 8)):
                created_at = start_date + timedelta(
                    seconds=random.randint(0, int((end_date - start_date).total_seconds()))
                )
                MasterQuestion.objects.create(
                    title=f"Master Question by {contributor.user.username} - {random.randint(1000, 9999)}",
                    passage_content=f"This is a test passage content for master question by {contributor.user.username}",
                    author=contributor,
                    approval_status='approved',
                    created_at=created_at
                )
                activities_created['master_questions'] += 1
            
            # Create master options
            for _ in range(random.randint(3, 10)):
                created_at = start_date + timedelta(
                    seconds=random.randint(0, int((end_date - start_date).total_seconds()))
                )
                MasterOption.objects.create(
                    title=f"Master Option by {contributor.user.username} - {random.randint(1000, 9999)}",
                    option_content=f"This is a test option content by {contributor.user.username}",
                    author=contributor,
                    approval_status='approved',
                    created_at=created_at
                )
                activities_created['master_options'] += 1
            
            # Create blogs
            for _ in range(random.randint(1, 5)):
                created_at = start_date + timedelta(
                    seconds=random.randint(0, int((end_date - start_date).total_seconds()))
                )
                BlogPost.objects.create(
                    title=f"Blog Post by {contributor.user.username} - {random.randint(1000, 9999)}",
                    content=f"This is a comprehensive blog post content written by {contributor.user.username}. It contains valuable information and insights.",
                    author=contributor,
                    approval_status='approved',
                    created_at=created_at
                )
                activities_created['blogs'] += 1
        
        # Create previous year questions (referencing existing questions)
        questions = list(Question.objects.all()[:10])  # Use first 10 questions to avoid slug conflicts
        subcourses = list(SubCourse.objects.all())

        for i, question in enumerate(questions):
            created_at = start_date + timedelta(
                seconds=random.randint(0, int((end_date - start_date).total_seconds()))
            )
            year = random.randint(2020, 2023)
            month = random.choice(['January', 'February', 'March', 'April', 'May', 'June'])

            # Create unique slug to avoid conflicts
            unique_id = f"{question.question_id}-{year}-{month}-{i}"

            pyq = PreviousYearQuestion(
                question=question,
                year=year,
                month=month,
                course=random.choice(courses),
                exams=random.choice(subcourses),
                approval_status='approved',
                created_at=created_at
            )
            # Set slug manually to avoid conflicts
            pyq.slug = f"pyq-{unique_id}-{random.randint(1000, 9999)}"
            pyq.save()
            activities_created['previous_questions'] += 1
        
        total_activities = sum(activities_created.values())
        self.log_result("Random Activities Creation", True, 
                       f"Created {total_activities} total activities", activities_created)
        
        return True
    
    def calculate_all_earnings(self):
        """Calculate earnings for all contributors"""
        print("💰 Calculating earnings for all contributors...")
        
        calculated_count = 0
        for contributor in self.contributors:
            try:
                # Calculate for different periods
                for period in ['monthly', 'weekly']:
                    EarningCalculationService.calculate_and_update_earnings(contributor, period)
                calculated_count += 1
            except Exception as e:
                self.log_result(f"Earning Calculation for {contributor.user.username}", False, str(e))
        
        self.log_result("Earnings Calculation", True, 
                       f"Calculated earnings for {calculated_count} contributors")
        
        return True

    def get_jwt_token(self, user):
        """Get JWT token for a user"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)

    def add_to_postman_collection(self, name, method, url, description, headers, body, status_code, response_data):
        """Add API test to Postman collection"""
        item = {
            "name": name,
            "request": {
                "method": method,
                "header": [{"key": k, "value": v} for k, v in headers.items()],
                "url": {
                    "raw": f"{{{{base_url}}}}{url}",
                    "host": ["{{base_url}}"],
                    "path": url.strip('/').split('/')
                },
                "description": description
            },
            "response": [{
                "name": f"Success Response ({status_code})",
                "originalRequest": {
                    "method": method,
                    "header": [{"key": k, "value": v} for k, v in headers.items()],
                    "url": {
                        "raw": f"{{{{base_url}}}}{url}",
                        "host": ["{{base_url}}"],
                        "path": url.strip('/').split('/')
                    }
                },
                "status": f"{status_code}",
                "code": status_code,
                "_postman_previewlanguage": "json",
                "header": [
                    {"key": "Content-Type", "value": "application/json"}
                ],
                "body": json.dumps(response_data, indent=2)
            }]
        }

        if body:
            item["request"]["body"] = {
                "mode": "raw",
                "raw": json.dumps(body, indent=2),
                "options": {
                    "raw": {
                        "language": "json"
                    }
                }
            }

        self.postman_data["item"].append(item)

    def test_dashboard_api(self):
        """Test the contributor dashboard API"""
        print("🔍 Testing Dashboard API...")

        test_contributor = self.contributors[0]
        token = self.get_jwt_token(test_contributor.user)

        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        response = self.api_client.get('/api/contributor/dashboard/')

        if response.status_code == 200:
            data = response.json()

            # Check if earnings data is present
            if 'earnings' in data:
                earnings = data['earnings']

                # Add to Postman collection
                self.add_to_postman_collection(
                    "Dashboard API",
                    "GET",
                    "/api/contributor/dashboard/",
                    "Get contributor dashboard with earnings information",
                    {"Authorization": f"Bearer {token}"},
                    None,
                    response.status_code,
                    data
                )

                self.log_result("Dashboard API", True,
                               f"Dashboard loaded successfully with earnings data",
                               {
                                   'contributor': data.get('contributor'),
                                   'total_points': earnings.get('total_lifetime_earnings', {}).get('total_points', 0),
                                   'current_month_points': earnings.get('current_month_earnings', {}).get('total_points', 0)
                               })
            else:
                self.log_result("Dashboard API", False, "No earnings data in dashboard response")
        else:
            self.log_result("Dashboard API", False, f"API returned status {response.status_code}")

        return response.status_code == 200

    def test_earnings_list_api(self):
        """Test the earnings list API"""
        print("📋 Testing Earnings List API...")

        test_contributor = self.contributors[0]
        token = self.get_jwt_token(test_contributor.user)

        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        response = self.api_client.get('/api/contributor/earnings/')

        if response.status_code == 200:
            data = response.json()

            self.add_to_postman_collection(
                "Earnings List API",
                "GET",
                "/api/contributor/earnings/",
                "Get list of contributor earnings",
                {"Authorization": f"Bearer {token}"},
                None,
                response.status_code,
                data
            )

            self.log_result("Earnings List API", True,
                           f"Retrieved {len(data)} earning records")
        else:
            self.log_result("Earnings List API", False, f"API returned status {response.status_code}")

        return response.status_code == 200

    def test_all_apis(self):
        """Test all earning APIs"""
        print("🚀 Testing all earning APIs...")

        api_tests = [
            self.test_dashboard_api,
            self.test_earnings_list_api,
            # Add more API tests here
        ]

        passed = 0
        failed = 0

        for test in api_tests:
            try:
                if test():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                self.log_result(f"API Test {test.__name__}", False, str(e))
                failed += 1

        self.log_result("API Testing Summary", failed == 0,
                       f"Passed: {passed}, Failed: {failed}")

        return failed == 0

    def test_data_isolation(self):
        """Test that contributors can only see their own data"""
        print("🔒 Testing data isolation...")

        # Test with two different contributors
        contributor1 = self.contributors[0]
        contributor2 = self.contributors[1]

        token1 = self.get_jwt_token(contributor1.user)
        token2 = self.get_jwt_token(contributor2.user)

        # Get earnings for contributor 1
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {token1}')
        response1 = self.api_client.get('/api/contributor/earnings/')

        # Get earnings for contributor 2
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {token2}')
        response2 = self.api_client.get('/api/contributor/earnings/')

        if response1.status_code == 200 and response2.status_code == 200:
            data1 = response1.json()
            data2 = response2.json()

            # Check that each contributor only sees their own data
            contributor1_usernames = set()
            contributor2_usernames = set()

            for earning in data1:
                contributor1_usernames.add(earning.get('contributor_username'))

            for earning in data2:
                contributor2_usernames.add(earning.get('contributor_username'))

            isolation_success = (
                len(contributor1_usernames) <= 1 and
                len(contributor2_usernames) <= 1 and
                contributor1_usernames != contributor2_usernames
            )

            self.log_result("Data Isolation Test", isolation_success,
                           f"Contributor 1 sees: {contributor1_usernames}, "
                           f"Contributor 2 sees: {contributor2_usernames}")
        else:
            self.log_result("Data Isolation Test", False, "API calls failed")

        return True

    def generate_postman_collection(self):
        """Generate Postman collection file"""
        print("📄 Generating Postman collection...")

        # Add environment variables
        self.postman_data["variable"] = [
            {
                "key": "base_url",
                "value": "http://localhost:8000",
                "type": "string"
            },
            {
                "key": "auth_token",
                "value": "your_jwt_token_here",
                "type": "string"
            }
        ]

        # Save to file
        with open('contributor_earning_api_collection.json', 'w') as f:
            json.dump(self.postman_data, f, indent=2)

        self.log_result("Postman Collection Generation", True,
                       f"Generated collection with {len(self.postman_data['item'])} endpoints")

        return True

    def generate_markdown_documentation(self):
        """Generate comprehensive markdown documentation"""
        print("📝 Generating markdown documentation...")

        md_content = self._create_markdown_content()

        with open('contributor_earning_api_documentation.md', 'w') as f:
            f.write(md_content)

        self.log_result("Markdown Documentation Generation", True,
                       "Generated comprehensive API documentation")

        return True

    def run_comprehensive_test(self):
        """Run all tests and generate documentation"""
        print("🎯 Starting Comprehensive Contributor Earning System Test")
        print("=" * 80)

        # Setup and data creation
        if not self.create_test_data():
            print("❌ Test data creation failed, aborting")
            return False

        if not self.create_random_activities():
            print("❌ Activity creation failed, aborting")
            return False

        if not self.calculate_all_earnings():
            print("❌ Earnings calculation failed, aborting")
            return False

        # API testing
        self.test_all_apis()
        self.test_data_isolation()

        # Documentation generation
        self.generate_postman_collection()
        self.generate_markdown_documentation()

        # Summary
        passed = sum(1 for result in self.test_results if result['success'])
        failed = len(self.test_results) - passed

        print("=" * 80)
        print(f"📊 Final Test Summary:")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")

        if failed == 0:
            print("🎉 All tests passed! Earning system is working perfectly.")
            print("📄 Generated files:")
            print("   - contributor_earning_api_collection.json (Postman collection)")
            print("   - contributor_earning_api_documentation.md (API documentation)")
        else:
            print("⚠️  Some tests failed. Please check the implementation.")

        return failed == 0

    def cleanup(self):
        """Cleanup test data"""
        try:
            print("🧹 Cleaning up test data...")

            # Delete in reverse order to avoid foreign key constraints
            ContributorEarning.objects.filter(contributor__in=self.contributors).delete()
            PreviousYearQuestion.objects.filter(question__author__in=self.contributors).delete()
            BlogPost.objects.filter(author__in=self.contributors).delete()
            MasterOption.objects.filter(author__in=self.contributors).delete()
            MasterQuestion.objects.filter(author__in=self.contributors).delete()
            Question.objects.filter(author__in=self.contributors).delete()

            for contributor in self.contributors:
                contributor.user.delete()

            for config in self.points_configs:
                config.delete()

            # Clean up subjects, topics, courses
            Subject.objects.filter(name__startswith="Test Subject").delete()
            Course.objects.filter(name__startswith="Test Course").delete()

            print("✅ Cleanup completed successfully")

        except Exception as e:
            print(f"⚠️  Cleanup error: {e}")

    def _create_markdown_content(self):
        """Create comprehensive markdown documentation content"""
        return '''# Contributor Earning System API Documentation

## Overview
This document provides comprehensive documentation for the Contributor Earning System APIs. The system allows contributors to earn points based on their activities and track their earnings over different time periods.

## Authentication
All APIs require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Base URL
```
http://localhost:8000/api/contributor/
```

## API Endpoints

### 1. Dashboard API
**Endpoint:** `GET /dashboard/`
**Description:** Get contributor dashboard with comprehensive earnings information

**Response Example:**
```json
{
  "contributor": "test_contributor_1",
  "earnings": {
    "current_month_earnings": {
      "total_points": 150.00,
      "activity_breakdown": {
        "normal_questions": {"count": 10, "points": 50.00},
        "master_questions": {"count": 5, "points": 100.00},
        "blogs": {"count": 2, "points": 30.00}
      }
    },
    "total_lifetime_earnings": {
      "total_points": 500.00,
      "earning_records_count": 3
    },
    "points_configuration": {
      "name": "Premium Points",
      "is_custom": true,
      "normal_questions": 10,
      "master_questions": 20
    }
  }
}
```

### 2. Earnings List API
**Endpoint:** `GET /earnings/`
**Description:** Get paginated list of contributor earnings

**Query Parameters:**
- `period_type` (optional): Filter by period type (daily, weekly, monthly, yearly)
- `is_paid` (optional): Filter by payment status (true/false)

**Response Example:**
```json
[
  {
    "id": 1,
    "contributor_username": "test_contributor_1",
    "period_type": "monthly",
    "period_start": "2024-01-01T00:00:00Z",
    "period_end": "2024-01-31T23:59:59Z",
    "total_points": 150.00,
    "is_paid": false,
    "activity_breakdown": {
      "normal_questions": {"count": 10, "points": 50.00},
      "master_questions": {"count": 5, "points": 100.00}
    }
  }
]
```

### 3. Earnings Summary API
**Endpoint:** `GET /earnings/summary/`
**Description:** Get earnings summary for a specific period

**Query Parameters:**
- `period_type` (required): Period type (daily, weekly, monthly, yearly, lifetime)

**Response Example:**
```json
{
  "period_type": "monthly",
  "period_start": "2024-01-01T00:00:00Z",
  "period_end": "2024-01-31T23:59:59Z",
  "total_points": 150.00,
  "total_earnings": 150.00,
  "activity_breakdown": {
    "normal_questions": {"count": 10, "points": 50.00},
    "master_questions": {"count": 5, "points": 100.00},
    "master_options": {"count": 8, "points": 40.00},
    "blogs": {"count": 2, "points": 30.00},
    "previous_questions": {"count": 3, "points": 24.00}
  },
  "is_paid": false,
  "paid_at": null
}
```

### 4. Total Earnings API
**Endpoint:** `GET /earnings/total/`
**Description:** Get total lifetime earnings for the contributor

**Response Example:**
```json
{
  "total_points": 500.00,
  "total_earnings": 500.00,
  "earning_records_count": 5
}
```

### 5. Points Configuration API
**Endpoint:** `GET /points-config/`
**Description:** Get the points configuration for the contributor

**Response Example:**
```json
{
  "points_configuration": {
    "id": 2,
    "name": "Premium Points",
    "normal_questions": 10,
    "master_questions": 20,
    "master_options": 5,
    "blogs": 15,
    "previous_questions": 8,
    "assigned_contributors_count": 3
  },
  "is_custom": true,
  "contributor": "test_contributor_1"
}
```

### 6. Recalculate Earnings API
**Endpoint:** `POST /earnings/recalculate/`
**Description:** Manually recalculate earnings for a specific period

**Request Body:**
```json
{
  "period_type": "monthly"
}
```

**Response Example:**
```json
{
  "message": "Earnings recalculated successfully",
  "earning": {
    "id": 1,
    "total_points": 150.00,
    "period_type": "monthly",
    "activity_breakdown": {
      "normal_questions": {"count": 10, "points": 50.00}
    }
  }
}
```

## Data Isolation
- Each contributor can only access their own earning data
- API responses are filtered by the authenticated user's contributor profile
- Cross-contributor data access is prevented at the API level

## Point Calculation
Points are calculated based on the contributor's assigned point configuration:
- **Normal Questions:** Configurable points per question
- **Master Questions:** Higher points for complex questions
- **Master Options:** Points for creating question options
- **Blogs:** Points for writing blog posts
- **Previous Questions:** Points for historical question data

## Error Responses
All APIs return appropriate HTTP status codes:
- `200`: Success
- `400`: Bad Request (invalid parameters)
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `500`: Internal Server Error

## Testing with Postman
1. Import the generated Postman collection
2. Set the `base_url` variable to your server URL
3. Obtain a JWT token through the login API
4. Set the `auth_token` variable with your JWT token
5. Run the collection to test all endpoints

## Rate Limiting
APIs may be rate-limited to prevent abuse. Check response headers for rate limit information.

## Support
For technical support or questions about the earning system, contact the development team.
'''


def main():
    """Main function to run comprehensive tests"""
    tester = ComprehensiveEarningTester()
    try:
        success = tester.run_comprehensive_test()
        return 0 if success else 1
    finally:
        tester.cleanup()


if __name__ == "__main__":
    exit(main())
