{"info": {"name": "Contributor Earning System - Complete API Collection", "description": "Comprehensive API collection for testing all contributor earning endpoints with real data examples", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set authorization header if auth_token is available", "if (pm.variables.get('auth_token')) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: 'Bearer ' + pm.variables.get('auth_token')", "    });", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}], "item": [{"name": "Get Contributor Dashboard", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/contributor/dashboard/", "host": ["{{base_url}}"], "path": ["api", "contributor", "dashboard"]}, "description": "Retrieve comprehensive dashboard information including earnings, activity summary, and points breakdown for the authenticated contributor."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/contributor/dashboard/", "host": ["{{base_url}}"], "path": ["api", "contributor", "dashboard"]}, "description": "Retrieve comprehensive dashboard information including earnings, activity summary, and points breakdown for the authenticated contributor."}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"contributor\": \"testuser105\",\n  \"questions_summary\": {\n    \"questions\": {\n      \"total\": {\n        \"created\": 34,\n        \"approved\": 4,\n        \"pending\": 30,\n        \"rejected\": 0\n      }\n    },\n    \"master_questions\": {\n      \"total\": {\n        \"created\": 5,\n        \"approved\": 0,\n        \"pending\": 5,\n        \"rejected\": 0\n      }\n    },\n    \"master_options\": {\n      \"total\": {\n        \"created\": 3,\n        \"approved\": 0,\n        \"pending\": 3,\n        \"rejected\": 0\n      }\n    },\n    \"blogs\": {\n      \"total\": {\n        \"created\": 6,\n        \"approved\": 1,\n        \"pending\": 5,\n        \"rejected\": 0\n      }\n    },\n    \"previous_questions\": {\n      \"total\": {\n        \"created\": 9,\n        \"approved\": 7,\n        \"pending\": 2,\n        \"rejected\": 0\n      }\n    }\n  },\n  \"current_month_data\": {\n    \"questions\": {\n      \"daily\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"weekly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_questions\": {\n      \"daily\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"weekly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"blogs\": {\n      \"daily\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"weekly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_options\": {\n      \"daily\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"weekly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"previous_questions\": {\n      \"daily\": {\n        \"created\": 7,\n        \"approved\": 7,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"weekly\": {\n        \"created\": 7,\n        \"approved\": 7,\n        \"pending\": 0,\n        \"rejected\": 0\n      },\n      \"monthly\": {\n        \"created\": 7,\n        \"approved\": 7,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    }\n  },\n  \"previous_month_data\": {\n    \"questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"blogs\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_options\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"previous_questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    }\n  },\n  \"third_month_data\": {\n    \"questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"blogs\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"master_options\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    },\n    \"previous_questions\": {\n      \"monthly\": {\n        \"created\": 0,\n        \"approved\": 0,\n        \"pending\": 0,\n        \"rejected\": 0\n      }\n    }\n  },\n  \"current_month_points\": {\n    \"normal_questions\": 0,\n    \"master_questions\": 0,\n    \"master_options\": 0,\n    \"blogs\": 0,\n    \"previous_questions\": 7,\n    \"total_points\": 7\n  },\n  \"previous_month_points\": {\n    \"normal_questions\": 0,\n    \"master_questions\": 0,\n    \"master_options\": 0,\n    \"blogs\": 0,\n    \"previous_questions\": 0,\n    \"total_points\": 0\n  },\n  \"third_month_points\": {\n    \"normal_questions\": 0,\n    \"master_questions\": 0,\n    \"master_options\": 0,\n    \"blogs\": 0,\n    \"previous_questions\": 0,\n    \"total_points\": 0\n  },\n  \"earnings\": {\n    \"current_month_earnings\": {\n      \"period_type\": \"monthly\",\n      \"period_start\": \"2025-07-01T00:00:00Z\",\n      \"period_end\": \"2025-07-31T23:59:59.999999Z\",\n      \"total_points\": 7.0,\n      \"total_earnings\": 0.0,\n      \"activity_breakdown\": {\n        \"normal_questions\": {\n          \"count\": 0,\n          \"points\": 0.0\n        },\n        \"master_questions\": {\n          \"count\": 0,\n          \"points\": 0.0\n        },\n        \"master_options\": {\n          \"count\": 0,\n          \"points\": 0.0\n        },\n        \"blogs\": {\n          \"count\": 0,\n          \"points\": 0.0\n        },\n        \"previous_questions\": {\n          \"count\": 7,\n          \"points\": 7.0\n        }\n      },\n      \"is_paid\": false,\n      \"paid_at\": null\n    },\n    \"total_lifetime_earnings\": {\n      \"total_points\": 7.0,\n      \"total_earnings\": 0.0,\n      \"earning_records_count\": 1\n    },\n    \"points_configuration\": {\n      \"name\": \"basic\",\n      \"is_custom\": false,\n      \"normal_questions\": 1,\n      \"master_questions\": 2,\n      \"master_options\": 2,\n      \"blogs\": 5,\n      \"previous_questions\": 1\n    },\n    \"earning_status\": {\n      \"is_paid\": false,\n      \"paid_at\": null\n    }\n  }\n}"}]}, {"name": "Get Earnings List", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/contributor/earnings/", "host": ["{{base_url}}"], "path": ["api", "contributor", "earnings"]}, "description": "Retrieve paginated list of all earning records for the authenticated contributor."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/contributor/earnings/", "host": ["{{base_url}}"], "path": ["api", "contributor", "earnings"]}, "description": "Retrieve paginated list of all earning records for the authenticated contributor."}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": 24,\n    \"contributor\": 1,\n    \"contributor_username\": \"testuser105\",\n    \"period_type\": \"monthly\",\n    \"period_start\": \"2025-07-01T05:30:00+05:30\",\n    \"period_end\": \"2025-08-01T05:29:59.999999+05:30\",\n    \"normal_questions_count\": 0,\n    \"master_questions_count\": 0,\n    \"master_options_count\": 0,\n    \"blogs_count\": 0,\n    \"previous_questions_count\": 7,\n    \"normal_questions_points\": \"0.00\",\n    \"master_questions_points\": \"0.00\",\n    \"master_options_points\": \"0.00\",\n    \"blogs_points\": \"0.00\",\n    \"previous_questions_points\": \"7.00\",\n    \"total_points\": \"7.00\",\n    \"total_earnings\": \"0.00\",\n    \"points_config_used\": 1,\n    \"points_config_name\": \"basic\",\n    \"is_paid\": false,\n    \"paid_at\": null,\n    \"created_at\": \"2025-07-22T14:37:44.403307+05:30\",\n    \"updated_at\": \"2025-07-22T14:37:44.406700+05:30\",\n    \"activity_breakdown\": {\n      \"normal_questions\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"master_questions\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"master_options\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"blogs\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"previous_questions\": {\n        \"count\": 7,\n        \"points\": 7.0\n      }\n    }\n  }\n]"}]}, {"name": "Get Monthly Earnings", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/contributor/earnings/", "host": ["{{base_url}}"], "path": ["api", "contributor", "earnings"], "query": [{"key": "period_type", "value": "monthly", "description": "Parameter: period_type"}]}, "description": "Retrieve earnings filtered by period type (monthly)."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/contributor/earnings/", "host": ["{{base_url}}"], "path": ["api", "contributor", "earnings"], "query": [{"key": "period_type", "value": "monthly", "description": "Parameter: period_type"}]}, "description": "Retrieve earnings filtered by period type (monthly)."}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": 24,\n    \"contributor\": 1,\n    \"contributor_username\": \"testuser105\",\n    \"period_type\": \"monthly\",\n    \"period_start\": \"2025-07-01T05:30:00+05:30\",\n    \"period_end\": \"2025-08-01T05:29:59.999999+05:30\",\n    \"normal_questions_count\": 0,\n    \"master_questions_count\": 0,\n    \"master_options_count\": 0,\n    \"blogs_count\": 0,\n    \"previous_questions_count\": 7,\n    \"normal_questions_points\": \"0.00\",\n    \"master_questions_points\": \"0.00\",\n    \"master_options_points\": \"0.00\",\n    \"blogs_points\": \"0.00\",\n    \"previous_questions_points\": \"7.00\",\n    \"total_points\": \"7.00\",\n    \"total_earnings\": \"0.00\",\n    \"points_config_used\": 1,\n    \"points_config_name\": \"basic\",\n    \"is_paid\": false,\n    \"paid_at\": null,\n    \"created_at\": \"2025-07-22T14:37:44.403307+05:30\",\n    \"updated_at\": \"2025-07-22T14:37:44.406700+05:30\",\n    \"activity_breakdown\": {\n      \"normal_questions\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"master_questions\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"master_options\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"blogs\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"previous_questions\": {\n        \"count\": 7,\n        \"points\": 7.0\n      }\n    }\n  }\n]"}]}, {"name": "Get Unpaid Earnings", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/contributor/earnings/", "host": ["{{base_url}}"], "path": ["api", "contributor", "earnings"], "query": [{"key": "is_paid", "value": "false", "description": "Parameter: is_paid"}]}, "description": "Retrieve earnings filtered by payment status (unpaid)."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/contributor/earnings/", "host": ["{{base_url}}"], "path": ["api", "contributor", "earnings"], "query": [{"key": "is_paid", "value": "false", "description": "Parameter: is_paid"}]}, "description": "Retrieve earnings filtered by payment status (unpaid)."}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": 24,\n    \"contributor\": 1,\n    \"contributor_username\": \"testuser105\",\n    \"period_type\": \"monthly\",\n    \"period_start\": \"2025-07-01T05:30:00+05:30\",\n    \"period_end\": \"2025-08-01T05:29:59.999999+05:30\",\n    \"normal_questions_count\": 0,\n    \"master_questions_count\": 0,\n    \"master_options_count\": 0,\n    \"blogs_count\": 0,\n    \"previous_questions_count\": 7,\n    \"normal_questions_points\": \"0.00\",\n    \"master_questions_points\": \"0.00\",\n    \"master_options_points\": \"0.00\",\n    \"blogs_points\": \"0.00\",\n    \"previous_questions_points\": \"7.00\",\n    \"total_points\": \"7.00\",\n    \"total_earnings\": \"0.00\",\n    \"points_config_used\": 1,\n    \"points_config_name\": \"basic\",\n    \"is_paid\": false,\n    \"paid_at\": null,\n    \"created_at\": \"2025-07-22T14:37:44.403307+05:30\",\n    \"updated_at\": \"2025-07-22T14:37:44.406700+05:30\",\n    \"activity_breakdown\": {\n      \"normal_questions\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"master_questions\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"master_options\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"blogs\": {\n        \"count\": 0,\n        \"points\": 0.0\n      },\n      \"previous_questions\": {\n        \"count\": 7,\n        \"points\": 7.0\n      }\n    }\n  }\n]"}]}]}