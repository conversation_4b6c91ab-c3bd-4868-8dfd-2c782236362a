from django.contrib.auth.models import User
from django.db import models
from students.models import Student
from django.utils.text import slugify
from questions.utils import generate_unique_slug
from django.utils.timezone import now, timedelta

class CustomrcareProfile(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="customrcare_profile"
    )
    role = models.CharField(max_length=30, default="customrcare")
    profile = models.CharField(max_length=255, default="customrcare")
    contact = models.BigIntegerField()
    account_status = models.CharField(max_length=30, default="active")
    
    slug = models.SlugField(unique=True, null=True, blank=True)  # Slug field

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(CustomrcareProfile ,
                f"{self.user.username}-{self.role}"
            )  # Generate slug from username and role
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.username} - {self.role} - {self.account_status}"


class Ticket(models.Model):
    customer = models.ForeignKey(
        CustomrcareProfile, on_delete=models.CASCADE, related_name="customrcare_profile"
    )
    student = models.ForeignKey(Student, on_delete=models.CASCADE, null=True, blank=True)
    ticket_assign = models.ForeignKey(
        CustomrcareProfile, on_delete=models.CASCADE, null=True, blank=True
    )
    ticket_status = models.CharField(max_length=50)
    priority = models.CharField(max_length=50)
    subject = models.CharField(max_length=255)
    description = models.TextField()
    resolve_summary = models.TextField(null=True, blank=True)
    attachments = models.ImageField(upload_to="attachments/", null=True, blank=True)
    tags = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    date_resolved = models.DateTimeField(null=True, blank=True)
    update_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, null=True, blank=True, max_length=1000)  # Slug field

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug( Ticket,
                f"{self.subject}-{self.ticket_status}"
            )  # Generate slug from subject and status
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Ticket for {self.customer.user.username} - {self.subject}"

class FrontendError(models.Model):
    """Enhanced frontend error logging with comprehensive tracking"""

    ERROR_TYPES = [
        ('JAVASCRIPT', 'JavaScript Error'),
        ('NETWORK', 'Network Error'),
        ('VALIDATION', 'Validation Error'),
        ('AUTHENTICATION', 'Authentication Error'),
        ('PERMISSION', 'Permission Error'),
        ('TIMEOUT', 'Timeout Error'),
        ('RESOURCE_LOAD', 'Resource Load Error'),
        ('API_ERROR', 'API Error'),
        ('RENDER_ERROR', 'Render Error'),
        ('USER_ACTION', 'User Action Error'),
        ('UNKNOWN', 'Unknown Error'),
    ]

    SEVERITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    BROWSER_TYPES = [
        ('CHROME', 'Chrome'),
        ('FIREFOX', 'Firefox'),
        ('SAFARI', 'Safari'),
        ('EDGE', 'Edge'),
        ('OPERA', 'Opera'),
        ('OTHER', 'Other'),
    ]

    # Enhanced fields
    error_type = models.CharField(max_length=20, choices=ERROR_TYPES, default='UNKNOWN')
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, default='MEDIUM')
    error_message = models.TextField()
    stack_trace = models.TextField(blank=True)

    # User and session information
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True)
    user_agent = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    # Browser and device information
    browser_name = models.CharField(max_length=20, choices=BROWSER_TYPES, default='OTHER')
    browser_version = models.CharField(max_length=50, blank=True)
    device_type = models.CharField(max_length=50, blank=True)  # mobile, desktop, tablet
    screen_resolution = models.CharField(max_length=20, blank=True)

    # Page and context information
    page_url = models.URLField(max_length=1000)
    page_title = models.CharField(max_length=500, blank=True)
    referrer_url = models.URLField(max_length=1000, blank=True)

    # Error context
    component_name = models.CharField(max_length=200, blank=True)
    function_name = models.CharField(max_length=200, blank=True)
    line_number = models.IntegerField(null=True, blank=True)
    column_number = models.IntegerField(null=True, blank=True)

    # Additional data
    error_data = models.JSONField(default=dict, blank=True)  # Store additional JSON content
    user_actions = models.JSONField(default=list, blank=True)  # User actions leading to error
    console_logs = models.JSONField(default=list, blank=True)  # Console logs

    # Status and resolution
    resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_frontend_errors')
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)

    # Occurrence tracking
    occurrence_count = models.IntegerField(default=1)
    first_occurrence = models.DateTimeField(auto_now_add=True)
    last_occurrence = models.DateTimeField(auto_now=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Frontend Error"
        verbose_name_plural = "Frontend Errors"
        indexes = [
            models.Index(fields=['error_type', 'created_at']),
            models.Index(fields=['severity', 'created_at']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['page_url', 'created_at']),
            models.Index(fields=['resolved', 'created_at']),
        ]

    def save(self, *args, **kwargs):
        # Check for duplicate errors and increment occurrence count
        if not self.pk:  # New error
            similar_error = FrontendError.objects.filter(
                error_message=self.error_message,
                page_url=self.page_url,
                user=self.user,
                created_at__gte=now() - timedelta(hours=1)  # Within last hour
            ).first()

            if similar_error:
                # Update existing error instead of creating new one
                similar_error.occurrence_count += 1
                similar_error.last_occurrence = now()
                similar_error.save()
                return similar_error

        # Save new error entry
        super().save(*args, **kwargs)

        # Define expiration period (7 days for enhanced logging)
        expiration_time = now() - timedelta(days=7)

        # Delete records older than 7 days (except critical errors)
        FrontendError.objects.filter(
            created_at__lt=expiration_time
        ).exclude(severity='CRITICAL').delete()

    def __str__(self):
        return f"{self.error_type} - {self.error_message[:50]} ({self.severity})"
    
from django.utils import timezone

class TemporaryImage(models.Model):
    image = models.ImageField(upload_to='uploads/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()

    def save(self, *args, **kwargs) -> None: 
        # Set expiration time to 10 minutes (or any desired duration)
        self.expires_at = timezone.now() + timedelta(minutes=5)
        super().save(*args, **kwargs)

class ExamGroupLevel(models.Model) :
    name = models.CharField(max_length=100)  # e.g., "Group A", "Group D"
    description = models.TextField(blank=True)
    difficulty_level = models.IntegerField(default=1)  # 1-10 scale
    passing_score = models.FloatField(default=60.0)  # Required score %

    def __str__(self):
        return self.name


class WalkAroundImage(models.Model):
    """Model for storing walk-around images with user association and status management."""

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='walk_around_images')
    image = models.ImageField(upload_to='walk_around_images/')
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.title or 'Walk Around Image'} ({self.status})"

    def save(self, *args, **kwargs):
        # If this image is being set to active, check the limit
        if self.status == 'active':
            # Count current active images for this user
            active_count = WalkAroundImage.objects.filter(
                user=self.user,
                status='active'
            ).exclude(pk=self.pk).count()

            # If already at limit (5), deactivate oldest active image
            if active_count >= 5:
                oldest_active = WalkAroundImage.objects.filter(
                    user=self.user,
                    status='active'
                ).exclude(pk=self.pk).order_by('created_at').first()

                if oldest_active:
                    oldest_active.status = 'inactive'
                    oldest_active.save()

        super().save(*args, **kwargs)