#!/usr/bin/env python3
"""
Generate test transactions for Razorpay dashboard verification
This script creates multiple test subscriptions to generate orders visible on Razorpay dashboard
"""

import os
import sys
import django
import time
import requests
import json
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from packages_and_subscriptions.models import Package, Subscription
from students.models import Student
from packages_and_subscriptions.payment_service import PaymentService

BASE_URL = "http://127.0.0.1:8000/api/packages"

def get_test_data():
    """Get available students and packages for testing"""
    students = list(Student.objects.all()[:5])  # Get first 5 students
    packages = list(Package.objects.filter(is_active=True, discount_price__gt=0))  # Only paid packages
    
    print(f"Found {len(students)} students and {len(packages)} paid packages for testing")
    
    return students, packages

def create_test_transaction_direct(student, package, test_name):
    """Create test transaction using direct backend method"""
    print(f"\n🔄 Creating {test_name}...")
    print(f"   Student: {student.user.username}")
    print(f"   Package: {package.name} (₹{package.discount_price})")
    
    try:
        payment_service = PaymentService()
        
        # Calculate pricing
        pricing_info = payment_service.calculate_final_price(
            package=package,
            coupon_code=None,
            gift_card_code=None,
            gift_card_pin=None,
            student=student
        )
        
        # Create Razorpay order (this will appear on dashboard)
        razorpay_order = payment_service.create_razorpay_order(
            amount=pricing_info['final_price'],
            student=student,
            package=package
        )
        
        # Create subscription
        subscription, invoice = payment_service.create_subscription(
            student=student,
            package=package,
            pricing_info=pricing_info,
            razorpay_order=razorpay_order
        )
        
        print(f"   ✅ Success!")
        print(f"   📋 Subscription ID: {subscription.id}")
        print(f"   💳 Razorpay Order ID: {razorpay_order['id']}")
        print(f"   💰 Amount: ₹{pricing_info['final_price']} ({int(pricing_info['final_price'] * 100)} paise)")
        
        return {
            'success': True,
            'subscription_id': subscription.id,
            'razorpay_order_id': razorpay_order['id'],
            'amount': float(pricing_info['final_price']),
            'student': student.user.username,
            'package': package.name
        }
        
    except Exception as e:
        print(f"   ❌ Failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def create_test_transaction_api(student, package, test_name):
    """Create test transaction using API endpoint"""
    print(f"\n🌐 Creating {test_name} via API...")
    print(f"   Student: {student.user.username}")
    print(f"   Package: {package.name} (₹{package.discount_price})")
    
    try:
        data = {
            'student': student.id,
            'package': package.id
        }
        
        response = requests.post(
            f"{BASE_URL}/v2/create-subscription/",
            json=data,
            timeout=15
        )
        
        if response.status_code == 201:
            result = response.json()
            print(f"   ✅ API Success!")
            print(f"   📋 Subscription ID: {result.get('subscription_id')}")
            print(f"   💳 Razorpay Order ID: {result.get('razorpay_order_id')}")
            print(f"   💰 Amount: ₹{result.get('final_price')}")
            
            return {
                'success': True,
                'subscription_id': result.get('subscription_id'),
                'razorpay_order_id': result.get('razorpay_order_id'),
                'amount': result.get('final_price'),
                'student': student.user.username,
                'package': package.name
            }
        else:
            print(f"   ❌ API Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return {'success': False, 'error': f"API Error: {response.status_code}"}
            
    except Exception as e:
        print(f"   ❌ API Failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def generate_test_transactions():
    """Generate multiple test transactions"""
    print("🚀 Generating Test Transactions for Razorpay Dashboard")
    print("=" * 60)
    
    students, packages = get_test_data()
    
    if not students:
        print("❌ No students found. Please create some student accounts first.")
        return
    
    if not packages:
        print("❌ No paid packages found. Please create some paid packages first.")
        return
    
    transactions = []
    
    # Test Scenario 1: Different packages with same student
    if len(packages) >= 2:
        student = students[0]
        for i, package in enumerate(packages[:3]):  # Test first 3 packages
            result = create_test_transaction_direct(
                student, package, 
                f"Test Transaction {i+1} - {package.package_type.title()} Package"
            )
            transactions.append(result)
            time.sleep(2)  # Small delay between transactions
    
    # Test Scenario 2: Same package with different students
    if len(students) >= 2:
        package = packages[0]  # Use first package
        for i, student in enumerate(students[1:4]):  # Test with 3 different students
            result = create_test_transaction_api(
                student, package,
                f"API Test Transaction {i+1} - Multiple Students"
            )
            transactions.append(result)
            time.sleep(2)
    
    # Test Scenario 3: Event package transactions
    event_packages = [p for p in packages if p.package_type == 'event']
    if event_packages and len(students) >= 2:
        for i, student in enumerate(students[:2]):
            result = create_test_transaction_direct(
                student, event_packages[0],
                f"Event Package Test {i+1}"
            )
            transactions.append(result)
            time.sleep(2)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST TRANSACTIONS SUMMARY")
    print("=" * 60)
    
    successful_transactions = [t for t in transactions if t.get('success')]
    failed_transactions = [t for t in transactions if not t.get('success')]
    
    print(f"✅ Successful Transactions: {len(successful_transactions)}")
    print(f"❌ Failed Transactions: {len(failed_transactions)}")
    
    if successful_transactions:
        print("\n🎯 SUCCESSFUL TRANSACTIONS (Check these on Razorpay Dashboard):")
        print("-" * 60)
        total_amount = 0
        for i, txn in enumerate(successful_transactions, 1):
            print(f"{i}. Order ID: {txn.get('razorpay_order_id')}")
            print(f"   Amount: ₹{txn.get('amount')} | Student: {txn.get('student')} | Package: {txn.get('package')}")
            total_amount += txn.get('amount', 0)
        
        print(f"\n💰 Total Test Amount: ₹{total_amount}")
        print(f"💳 Total Orders Created: {len(successful_transactions)}")
    
    if failed_transactions:
        print("\n⚠️ FAILED TRANSACTIONS:")
        print("-" * 30)
        for i, txn in enumerate(failed_transactions, 1):
            print(f"{i}. Error: {txn.get('error')}")
    
    print("\n" + "=" * 60)
    print("🎯 NEXT STEPS:")
    print("1. Login to your Razorpay Dashboard")
    print("2. Go to Payments > Orders section")
    print("3. You should see the orders created above")
    print("4. Orders will be in 'Created' status (waiting for payment)")
    print("5. You can use Razorpay test cards to complete payments if needed")
    print("\n🔗 Razorpay Test Dashboard: https://dashboard.razorpay.com/")
    print("=" * 60)

if __name__ == "__main__":
    generate_test_transactions()
